from aiogram.types import Rep<PERSON><PERSON><PERSON>boardMarkup, KeyboardButton, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.keyboard import Reply<PERSON><PERSON>boardBuilder, InlineKeyboardBuilder
from ..services.localization import LocalizationService


def get_main_keyboard(lang: str = 'en') -> ReplyKeyboardMarkup:
    """Get main menu keyboard"""
    builder = ReplyKeyboardBuilder()

    builder.add(KeyboardButton(text=LocalizationService.get_text('btn_profile', lang)))
    builder.add(KeyboardButton(text=LocalizationService.get_text('btn_settings', lang)))
    builder.add(KeyboardButton(text=LocalizationService.get_text('btn_help', lang)))
    builder.add(KeyboardButton(text=LocalizationService.get_text('btn_contact', lang)))

    builder.adjust(2)  # 2 buttons per row
    return builder.as_markup(resize_keyboard=True)


def get_settings_keyboard(lang: str = 'en') -> InlineKeyboardMarkup:
    """Get settings inline keyboard"""
    builder = InlineKeyboardBuilder()

    builder.add(InlineKeyboardButton(text=LocalizationService.get_text('btn_notifications', lang), callback_data="settings_notifications"))
    builder.add(InlineKeyboardButton(text=LocalizationService.get_text('btn_language', lang), callback_data="settings_language"))
    builder.add(InlineKeyboardButton(text=LocalizationService.get_text('btn_privacy', lang), callback_data="settings_privacy"))
    builder.add(InlineKeyboardButton(text=LocalizationService.get_text('btn_back_main', lang), callback_data="back_to_main"))

    builder.adjust(2)  # 2 buttons per row
    return builder.as_markup()


def get_language_keyboard(is_first_time: bool = False) -> InlineKeyboardMarkup:
    """Get language selection keyboard"""
    builder = InlineKeyboardBuilder()

    builder.add(InlineKeyboardButton(text="🇬🇧 English", callback_data="lang_en"))
    builder.add(InlineKeyboardButton(text="🇷🇺 Русский", callback_data="lang_ru"))
    builder.add(InlineKeyboardButton(text="🇺🇿 O‘zbek", callback_data="lang_uz"))

    # Only show back button if not first time (when user is in settings)
    if not is_first_time:
        builder.add(InlineKeyboardButton(text="⬅️ Back to Settings", callback_data="back_to_settings"))

    builder.adjust(2)  # 2 buttons per row
    return builder.as_markup()


def get_confirm_keyboard(lang: str = 'en') -> InlineKeyboardMarkup:
    """Get confirmation keyboard"""
    builder = InlineKeyboardBuilder()

    builder.add(InlineKeyboardButton(text=LocalizationService.get_text('btn_yes', lang), callback_data="confirm_yes"))
    builder.add(InlineKeyboardButton(text=LocalizationService.get_text('btn_no', lang), callback_data="confirm_no"))

    return builder.as_markup()
