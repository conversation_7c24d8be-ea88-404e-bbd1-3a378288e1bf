from aiogram.types import Reply<PERSON><PERSON>boardMarkup, KeyboardButton, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.keyboard import Reply<PERSON><PERSON>boardBuilder, InlineKeyboardBuilder


def get_main_keyboard() -> ReplyKeyboardMarkup:
    """Get main menu keyboard"""
    builder = ReplyKeyboardBuilder()
    
    builder.add(KeyboardButton(text="📊 Profile"))
    builder.add(KeyboardButton(text="⚙️ Settings"))
    builder.add(KeyboardButton(text="❓ Help"))
    builder.add(KeyboardButton(text="📞 Contact"))
    
    builder.adjust(2)  # 2 buttons per row
    return builder.as_markup(resize_keyboard=True)


def get_settings_keyboard() -> InlineKeyboardMarkup:
    """Get settings inline keyboard"""
    builder = InlineKeyboardBuilder()
    
    builder.add(InlineKeyboardButton(text="🔔 Notifications", callback_data="settings_notifications"))
    builder.add(InlineKeyboardButton(text="🌍 Language", callback_data="settings_language"))
    builder.add(InlineKeyboardButton(text="🔒 Privacy", callback_data="settings_privacy"))
    builder.add(InlineKeyboardButton(text="⬅️ Back to Main", callback_data="back_to_main"))
    
    builder.adjust(2)  # 2 buttons per row
    return builder.as_markup()


def get_language_keyboard() -> InlineKeyboardMarkup:
    """Get language selection keyboard"""
    builder = InlineKeyboardBuilder()
    
    builder.add(InlineKeyboardButton(text="🇬🇧 English", callback_data="lang_en"))
    builder.add(InlineKeyboardButton(text="🇷🇺 Русский", callback_data="lang_ru"))
    builder.add(InlineKeyboardButton(text="🇺🇿 O‘zbek", callback_data="lang_uz"))
    builder.add(InlineKeyboardButton(text="⬅️ Back to Settings", callback_data="back_to_settings"))
    
    builder.adjust(2)  # 2 buttons per row
    return builder.as_markup()


def get_confirm_keyboard() -> InlineKeyboardMarkup:
    """Get confirmation keyboard"""
    builder = InlineKeyboardBuilder()
    
    builder.add(InlineKeyboardButton(text="✅ Yes", callback_data="confirm_yes"))
    builder.add(InlineKeyboardButton(text="❌ No", callback_data="confirm_no"))
    
    return builder.as_markup() 
