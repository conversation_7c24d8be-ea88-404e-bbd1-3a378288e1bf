from django.db import models


class User(models.Model):
    chat_id = models.BigIntegerField(unique=True)
    username = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    first_name = models.CharField(max_length=255, null=True, blank=True)
    last_name = models.CharField(max_length=255, null=True, blank=True)
    language_code = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.username or f"User {self.chat_id}"

    class Meta:
        verbose_name = "Bot User"
        verbose_name_plural = "Bot Users"
