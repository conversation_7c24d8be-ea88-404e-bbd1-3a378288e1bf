from typing import Any, Awaitable, Callable, Dict
from aiogram import BaseMiddleware
from aiogram.types import TelegramObject, Message, CallbackQuery
import logging

logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseMiddleware):
    """Simple middleware for logging requests"""

    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        """Log request details"""
        if isinstance(event, (Message, CallbackQuery)):
            user_id = event.from_user.id
            username = event.from_user.username or event.from_user.first_name

            if isinstance(event, Message):
                content = event.text or "media"
            else:
                content = event.data

            logger.info(f"User {user_id} ({username}): {content}")

        try:
            return await handler(event, data)
        except Exception as e:
            logger.error(f"Handler error: {e}")
            raise