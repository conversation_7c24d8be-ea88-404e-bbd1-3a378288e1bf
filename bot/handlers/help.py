from aiogram import Router
from aiogram.filters import Command
from aiogram.types import Message
from .base import log_user_action, send_error_message
from ..services.localization import LocalizationService

# Create router for help commands
help_router = Router()


@help_router.message(Command("help"))
async def help_command(message: Message):
    """Handle /help command"""
    try:
        log_user_action(message, "help_command")

        lang = await LocalizationService.get_user_language(message.from_user.id)

        help_text = (
            f"{LocalizationService.get_text('help_title', lang)}\n\n"
            f"{LocalizationService.get_text('help_commands', lang)}\n\n"
            f"{LocalizationService.get_text('help_features', lang)}\n\n"
            f"{LocalizationService.get_text('help_footer', lang)}"
        )

        await message.answer(help_text, parse_mode="HTML")

    except Exception as e:
        await send_error_message(message, e)