from aiogram import Router
from aiogram.filters import Command
from aiogram.types import Message
from .base import log_user_action, send_error_message

# Create router for help commands
help_router = Router()


@help_router.message(Command("help"))
async def help_command(message: Message):
    """Handle /help command"""
    try:
        log_user_action(message, "help_command")

        help_text = (
            "🤖 <b>Bot Commands</b>\n\n"
            "/start - Start the bot and get main menu\n"
            "/help - Show this help message\n"
            "/profile - View your profile information\n\n"
            "<b>Features:</b>\n"
            "• User management and tracking\n"
            "• Interactive keyboards\n"
            "• Error handling and logging\n"
            "• Django integration\n\n"
            "Need more help? Contact the bot administrator."
        )

        await message.answer(help_text, parse_mode="HTML")

    except Exception as e:
        await send_error_message(message, e)