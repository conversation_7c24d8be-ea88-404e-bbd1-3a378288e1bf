from aiogram import Router
from aiogram.types import CallbackQuery
from .base import log_user_action, send_error_message
from ..keyboards import get_main_keyboard, get_settings_keyboard, get_language_keyboard
from ..services.user_service import UserService
from ..services.localization import LocalizationService

# Create router for callback queries
callback_router = Router()


@callback_router.callback_query(lambda c: c.data.startswith("settings_"))
async def handle_settings(callback: CallbackQuery):
    """Handle settings callback queries"""
    try:
        log_user_action(callback, f"settings_{callback.data}")

        # Get user language
        lang = await LocalizationService.get_user_language(callback.from_user.id)

        if callback.data == "settings_notifications":
            await callback.message.edit_text(
                LocalizationService.get_text('notifications_title', lang),
                parse_mode="HTML",
                reply_markup=get_settings_keyboard(lang)
            )

        elif callback.data == "settings_language":
            await callback.message.edit_text(
                LocalizationService.get_text('language_settings_title', lang),
                parse_mode="HTML",
                reply_markup=get_language_keyboard(is_first_time=False)
            )

        elif callback.data == "settings_privacy":
            await callback.message.edit_text(
                LocalizationService.get_text('privacy_title', lang),
                parse_mode="HTML",
                reply_markup=get_settings_keyboard(lang)
            )

        await callback.answer()

    except Exception as e:
        await send_error_message(callback, e)

@callback_router.callback_query(lambda c: c.data.startswith("lang_"))
async def handle_language(callback: CallbackQuery):
    """Handle language selection"""
    try:
        log_user_action(callback, f"language_{callback.data}")

        language_map = {
            "lang_en": "en",
            "lang_ru": "ru",
            "lang_uz": "uz",
        }

        lang_code = language_map.get(callback.data, "en")

        # Update user language in DB
        await UserService.update_user_async(callback.from_user.id, language_code=lang_code)

        # Get confirmation text in selected language
        confirm_text = LocalizationService.get_text('language_set', lang_code)

        # Show confirmation and main menu with proper keyboard
        await callback.message.edit_text(
            confirm_text,
            reply_markup=get_main_keyboard(lang_code)
        )
        await callback.answer()

    except Exception as e:
        await send_error_message(callback, e)


@callback_router.callback_query(lambda c: c.data.startswith("back_"))
async def handle_navigation(callback: CallbackQuery):
    """Handle navigation callbacks"""
    try:
        log_user_action(callback, f"navigation_{callback.data}")

        # Get user language
        lang = await LocalizationService.get_user_language(callback.from_user.id)

        if callback.data == "back_to_main":
            await callback.message.delete()
            await callback.message.answer(
                LocalizationService.get_text('main_menu_title', lang),
                parse_mode="HTML",
                reply_markup=get_main_keyboard(lang)
            )

        elif callback.data == "back_to_settings":
            await callback.message.edit_text(
                LocalizationService.get_text('settings_title', lang),
                parse_mode="HTML",
                reply_markup=get_settings_keyboard(lang)
            )

        await callback.answer()

    except Exception as e:
        await send_error_message(callback, e)


@callback_router.callback_query(lambda c: c.data.startswith("confirm_"))
async def handle_confirm(callback: CallbackQuery):
    """Handle confirmation callbacks"""
    try:
        log_user_action(callback, f"confirm_{callback.data}")

        # Get user language
        lang = await LocalizationService.get_user_language(callback.from_user.id)

        if callback.data == "confirm_yes":
            await callback.message.edit_text(
                LocalizationService.get_text('confirmed', lang),
                parse_mode="HTML"
            )
        elif callback.data == "confirm_no":
            await callback.message.edit_text(
                LocalizationService.get_text('cancelled', lang),
                parse_mode="HTML"
            )

        await callback.answer()

    except Exception as e:
        await send_error_message(callback, e)