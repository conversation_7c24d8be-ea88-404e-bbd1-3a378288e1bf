from aiogram import Router
from aiogram.types import CallbackQuery
from .base import log_user_action, send_error_message
from ..keyboards import get_main_keyboard, get_settings_keyboard, get_language_keyboard
from ..services.user_service import UserService

# Create router for callback queries
callback_router = Router()


@callback_router.callback_query(lambda c: c.data.startswith("settings_"))
async def handle_settings(callback: CallbackQuery):
    """Handle settings callback queries"""
    try:
        log_user_action(callback, f"settings_{callback.data}")

        if callback.data == "settings_notifications":
            await callback.message.edit_text(
                "🔔 <b>Notification Settings</b>\n\n"
                "Configure your notification preferences here.\n"
                "This feature is coming soon!",
                parse_mode="HTML",
                reply_markup=get_settings_keyboard()
            )

        elif callback.data == "settings_language":
            await callback.message.edit_text(
                "🌍 <b>Language Settings</b>\n\n"
                "Select your preferred language:",
                parse_mode="HTML",
                reply_markup=get_language_keyboard()
            )

        elif callback.data == "settings_privacy":
            await callback.message.edit_text(
                "🔒 <b>Privacy Settings</b>\n\n"
                "Manage your privacy preferences here.\n"
                "This feature is coming soon!",
                parse_mode="HTML",
                reply_markup=get_settings_keyboard()
            )

        await callback.answer()

    except Exception as e:
        await send_error_message(callback, e)

@callback_router.callback_query(lambda c: c.data.startswith("lang_"))
async def handle_language(callback: CallbackQuery):
    """Handle language selection"""
    try:
        log_user_action(callback, f"language_{callback.data}")

        language_map = {
            "lang_en": ("en", "🇬🇧 English selected!\n\nWelcome!"),
            "lang_ru": ("ru", "🇷🇺 Язык выбран!\n\nДобро пожаловать!"),
            "lang_uz": ("uz", "🇺🇿 Til tanlandi!\n\nXush kelibsiz!"),
        }

        lang_code, confirm_text = language_map.get(callback.data, ("en", "Language set!"))

        # Update user language in DB
        await UserService.update_user_async(callback.from_user.id, language_code=lang_code)

        # Show confirmation and main menu
        await callback.message.edit_text(confirm_text, reply_markup=get_main_keyboard())
        await callback.answer()

    except Exception as e:
        await send_error_message(callback, e)


@callback_router.callback_query(lambda c: c.data.startswith("back_"))
async def handle_navigation(callback: CallbackQuery):
    """Handle navigation callbacks"""
    try:
        log_user_action(callback, f"navigation_{callback.data}")

        if callback.data == "back_to_main":
            await callback.message.delete()
            await callback.message.answer(
                "🏠 <b>Main Menu</b>\n\n"
                "Welcome back! What would you like to do?",
                parse_mode="HTML",
                reply_markup=get_main_keyboard()
            )

        elif callback.data == "back_to_settings":
            await callback.message.edit_text(
                "⚙️ <b>Settings</b>\n\n"
                "Configure your bot preferences:",
                parse_mode="HTML",
                reply_markup=get_settings_keyboard()
            )

        await callback.answer()

    except Exception as e:
        await send_error_message(callback, e)


@callback_router.callback_query(lambda c: c.data.startswith("confirm_"))
async def handle_confirm(callback: CallbackQuery):
    """Handle confirmation callbacks"""
    try:
        log_user_action(callback, f"confirm_{callback.data}")

        if callback.data == "confirm_yes":
            await callback.message.edit_text(
                "✅ <b>Confirmed!</b>\n\n"
                "Your action has been completed successfully.",
                parse_mode="HTML"
            )
        elif callback.data == "confirm_no":
            await callback.message.edit_text(
                "❌ <b>Cancelled</b>\n\n"
                "Action has been cancelled.",
                parse_mode="HTML"
            )

        await callback.answer()

    except Exception as e:
        await send_error_message(callback, e)