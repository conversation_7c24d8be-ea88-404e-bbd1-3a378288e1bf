from aiogram.types import Message, CallbackQuery
from typing import Union
import logging

logger = logging.getLogger(__name__)


def log_user_action(event: Union[Message, CallbackQuery], action: str):
    """Log user actions for debugging and analytics"""
    user_id = event.from_user.id
    username = event.from_user.username or event.from_user.first_name
    logger.info(f"User {user_id} ({username}) performed action: {action}")


async def send_error_message(event: Union[Message, CallbackQuery], error: Exception):
    """Send error message to user"""
    logger.error(f"Error handling {type(event).__name__}: {error}")
    error_text = "Sorry, something went wrong. Please try again later."

    if isinstance(event, Message):
        await event.answer(error_text)
    elif isinstance(event, CallbackQuery):
        await event.answer(error_text, show_alert=True)