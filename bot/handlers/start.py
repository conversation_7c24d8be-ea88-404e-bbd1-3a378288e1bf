from aiogram import Router
from aiogram.filters import Command
from aiogram.types import Message
from .base import log_user_action, send_error_message
from ..services.user_service import UserService
from ..services.localization import LocalizationService
from ..keyboards import get_main_keyboard
from ..keyboards.main import get_language_keyboard

# Create router for start commands
start_router = Router()


@start_router.message(Command("start"))
async def start_command(message: Message):
    """Handle /start command"""
    try:
        log_user_action(message, "start_command")

        # Create or get user using async version - don't auto-set language
        user = await UserService.create_or_get_user_async(
            chat_id=message.from_user.id,
            username=message.from_user.username,
            first_name=message.from_user.first_name,
            last_name=message.from_user.last_name,
            language_code=None  # Force language selection
        )

        # If user has not set language, show language selection
        if not user.language_code:
            await message.answer(
                "Please choose your language / Iltimos, tilni tanlang / Пожалуйста, выберите язык:",
                reply_markup=get_language_keyboard(is_first_time=True)
            )
            return

        # Use user's language for welcome text
        lang = user.language_code
        name = message.from_user.first_name or 'User'

        welcome_text = LocalizationService.get_text('welcome_message', lang, name=name)
        keyboard = get_main_keyboard(lang)

        await message.answer(welcome_text, reply_markup=keyboard)

    except Exception as e:
        await send_error_message(message, e)
