from aiogram import Router
from aiogram.filters import Command
from aiogram.types import Message
from .base import log_user_action, send_error_message
from ..services.user_service import UserService
from ..keyboards import get_main_keyboard
from ..keyboards.main import get_language_keyboard

# Create router for start commands
start_router = Router()


@start_router.message(Command("start"))
async def start_command(message: Message):
    """Handle /start command"""
    try:
        log_user_action(message, "start_command")

        # Create or get user using async version
        user = await UserService.create_or_get_user_async(
            chat_id=message.from_user.id,
            username=message.from_user.username,
            first_name=message.from_user.first_name,
            last_name=message.from_user.last_name,
            language_code=message.from_user.language_code
        )

        # If user has not set language, show language selection
        if not user.language_code:
            await message.answer(
                "Please choose your language / Iltimos, tilni tanlang / Пожалуйста, выберите язык:",
                reply_markup=get_language_keyboard()
            )
            return

        # Use user's language for welcome text (simple example)
        lang = user.language_code
        if lang == 'ru':
            welcome_text = (
                "👋 Добро пожаловать!\n\n"
                "Я ваш Telegram-бот помощник. Вот что я умею:\n"
                "• Помогу с разными задачами\n"
                "• Предоставлю информацию и поддержку\n"
                "• И многое другое!\n\n"
                "Используйте кнопки ниже, чтобы начать:"
            )
        elif lang == 'uz':
            welcome_text = (
                "👋 Xush kelibsiz!\n\n"
                "Men sizning Telegram bot yordamchingizman. Mana nimalar qila olaman:\n"
                "• Turli vazifalarda yordam beraman\n"
                "• Ma'lumot va yordam taqdim etaman\n"
                "• Yana ko'p narsalar!\n\n"
                "Boshlash uchun quyidagi tugmalardan foydalaning:"
            )
        else:
            welcome_text = (
                f"👋 Welcome, {message.from_user.first_name or 'User'}!\n\n"
                "I'm your Telegram bot assistant. Here's what I can do:\n"
                "• Help you with various tasks\n"
                "• Provide information and support\n"
                "• And much more!\n\n"
                "Use the buttons below to get started:"
            )

        keyboard = get_main_keyboard()
        await message.answer(welcome_text, reply_markup=keyboard)

    except Exception as e:
        await send_error_message(message, e)