from aiogram import Router
from aiogram.filters import Command
from aiogram.types import Message
from .base import log_user_action, send_error_message
from ..services.user_service import UserService
from ..services.localization import LocalizationService

# Create router for profile commands
profile_router = Router()


@profile_router.message(Command("profile"))
async def profile_command(message: Message):
    """Handle /profile command"""
    try:
        log_user_action(message, "profile_command")

        # Get user from database using async version
        user = await UserService.get_user_by_chat_id_async(message.from_user.id)
        lang = user.language_code if user and user.language_code else 'en'

        if not user:
            await message.answer(LocalizationService.get_text('profile_not_found', lang))
            return

        # Format dates
        created_at = user.created_at.strftime('%B %d, %Y') if user.created_at else LocalizationService.get_text('not_set', lang)
        updated_at = user.updated_at.strftime('%B %d, %Y at %H:%M') if user.updated_at else LocalizationService.get_text('not_set', lang)

        profile_text = (
            f"{LocalizationService.get_text('profile_title', lang)}\n\n"
            f"{LocalizationService.get_text('profile_name', lang, name=user.first_name or LocalizationService.get_text('not_set', lang))}\n"
            f"{LocalizationService.get_text('profile_username', lang, username=user.username or LocalizationService.get_text('not_set', lang))}\n"
            f"{LocalizationService.get_text('profile_language', lang, language=LocalizationService.get_language_name(user.language_code) if user.language_code else LocalizationService.get_text('not_set', lang))}\n"
            f"{LocalizationService.get_text('profile_member_since', lang, date=created_at)}\n"
            f"{LocalizationService.get_text('profile_last_updated', lang, date=updated_at)}\n\n"
            f"{LocalizationService.get_text('profile_chat_id', lang, chat_id=user.chat_id)}"
        )

        await message.answer(profile_text, parse_mode="HTML")

    except Exception as e:
        await send_error_message(message, e)