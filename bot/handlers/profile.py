from aiogram import Router
from aiogram.filters import Command
from aiogram.types import Message
from .base import log_user_action, send_error_message
from ..services.user_service import UserService

# Create router for profile commands
profile_router = Router()


@profile_router.message(Command("profile"))
async def profile_command(message: Message):
    """Handle /profile command"""
    try:
        log_user_action(message, "profile_command")

        # Get user from database using async version
        user = await UserService.get_user_by_chat_id_async(message.from_user.id)

        if not user:
            await message.answer("❌ User profile not found. Please use /start first.")
            return

        # Format dates
        created_at = user.created_at.strftime('%B %d, %Y') if user.created_at else 'Unknown'
        updated_at = user.updated_at.strftime('%B %d, %Y at %H:%M') if user.updated_at else 'Unknown'

        profile_text = (
            f"👤 <b>Your Profile</b>\n\n"
            f"<b>Name:</b> {user.first_name or 'Not set'}\n"
            f"<b>Username:</b> @{user.username or 'Not set'}\n"
            f"<b>Language:</b> {user.language_code or 'Not set'}\n"
            f"<b>Member since:</b> {created_at}\n"
            f"<b>Last updated:</b> {updated_at}\n\n"
            f"<b>Chat ID:</b> <code>{user.chat_id}</code>"
        )

        await message.answer(profile_text, parse_mode="HTML")

    except Exception as e:
        await send_error_message(message, e)