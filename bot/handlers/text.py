from aiogram import Router
from aiogram.types import Message
from .base import log_user_action, send_error_message
from ..keyboards import get_main_keyboard, get_settings_keyboard
from ..services.user_service import UserService
from ..services.localization import LocalizationService

# Create router for text messages
text_router = Router()


async def is_profile_button(message: Message) -> bool:
    """Check if message is profile button in any language"""
    if not message.text:
        return False
    user_lang = await LocalizationService.get_user_language(message.from_user.id)
    profile_text = LocalizationService.get_text('btn_profile', user_lang)
    return message.text == profile_text


@text_router.message(is_profile_button)
async def handle_profile_button(message: Message):
    """Handle profile button press"""
    try:
        log_user_action(message, "profile_button")

        user = await UserService.get_user_by_chat_id_async(message.from_user.id)
        lang = user.language_code if user and user.language_code else 'en'

        if not user:
            await message.answer(LocalizationService.get_text('profile_not_found', lang))
            return

        created_at = user.created_at.strftime('%B %d, %Y') if user.created_at else LocalizationService.get_text('not_set', lang)
        updated_at = user.updated_at.strftime('%B %d, %Y at %H:%M') if user.updated_at else LocalizationService.get_text('not_set', lang)

        profile_text = (
            f"{LocalizationService.get_text('profile_title', lang)}\n\n"
            f"{LocalizationService.get_text('profile_name', lang, name=user.first_name or LocalizationService.get_text('not_set', lang))}\n"
            f"{LocalizationService.get_text('profile_username', lang, username=user.username or LocalizationService.get_text('not_set', lang))}\n"
            f"{LocalizationService.get_text('profile_language', lang, language=LocalizationService.get_language_name(user.language_code) if user.language_code else LocalizationService.get_text('not_set', lang))}\n"
            f"{LocalizationService.get_text('profile_member_since', lang, date=created_at)}\n"
            f"{LocalizationService.get_text('profile_last_updated', lang, date=updated_at)}\n\n"
            f"{LocalizationService.get_text('profile_chat_id', lang, chat_id=user.chat_id)}"
        )

        await message.answer(profile_text, parse_mode="HTML")

    except Exception as e:
        await send_error_message(message, e)


async def is_settings_button(message: Message) -> bool:
    """Check if message is settings button in any language"""
    if not message.text:
        return False
    user_lang = await LocalizationService.get_user_language(message.from_user.id)
    settings_text = LocalizationService.get_text('btn_settings', user_lang)
    return message.text == settings_text


@text_router.message(is_settings_button)
async def handle_settings_button(message: Message):
    """Handle settings button press"""
    try:
        log_user_action(message, "settings_button")

        lang = await LocalizationService.get_user_language(message.from_user.id)

        await message.answer(
            LocalizationService.get_text('settings_title', lang),
            parse_mode="HTML",
            reply_markup=get_settings_keyboard(lang)
        )

    except Exception as e:
        await send_error_message(message, e)


async def is_help_button(message: Message) -> bool:
    """Check if message is help button in any language"""
    if not message.text:
        return False
    user_lang = await LocalizationService.get_user_language(message.from_user.id)
    help_text = LocalizationService.get_text('btn_help', user_lang)
    return message.text == help_text


@text_router.message(is_help_button)
async def handle_help_button(message: Message):
    """Handle help button press"""
    try:
        log_user_action(message, "help_button")

        lang = await LocalizationService.get_user_language(message.from_user.id)

        help_text = (
            f"{LocalizationService.get_text('help_title', lang)}\n\n"
            f"{LocalizationService.get_text('help_commands', lang)}\n\n"
            f"{LocalizationService.get_text('help_features', lang)}\n\n"
            f"{LocalizationService.get_text('help_footer', lang)}"
        )

        await message.answer(help_text, parse_mode="HTML")

    except Exception as e:
        await send_error_message(message, e)


async def is_contact_button(message: Message) -> bool:
    """Check if message is contact button in any language"""
    if not message.text:
        return False
    user_lang = await LocalizationService.get_user_language(message.from_user.id)
    contact_text = LocalizationService.get_text('btn_contact', user_lang)
    return message.text == contact_text


@text_router.message(is_contact_button)
async def handle_contact_button(message: Message):
    """Handle contact button press"""
    try:
        log_user_action(message, "contact_button")

        lang = await LocalizationService.get_user_language(message.from_user.id)

        contact_text = (
            f"{LocalizationService.get_text('contact_title', lang)}\n\n"
            f"{LocalizationService.get_text('contact_admin', lang)}\n"
            f"{LocalizationService.get_text('contact_email', lang)}\n"
            f"{LocalizationService.get_text('contact_support', lang)}\n\n"
            f"{LocalizationService.get_text('contact_footer', lang)}"
        )

        await message.answer(contact_text, parse_mode="HTML")

    except Exception as e:
        await send_error_message(message, e)


@text_router.message()
async def handle_unknown_text(message: Message):
    """Handle unknown text messages"""
    try:
        log_user_action(message, "unknown_text")

        lang = await LocalizationService.get_user_language(message.from_user.id)

        await message.answer(
            LocalizationService.get_text('unknown_message', lang),
            reply_markup=get_main_keyboard(lang)
        )

    except Exception as e:
        await send_error_message(message, e)