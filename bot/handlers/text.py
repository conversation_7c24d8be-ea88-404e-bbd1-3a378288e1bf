from aiogram import Router
from aiogram.types import Message
from .base import log_user_action, send_error_message
from ..keyboards import get_main_keyboard, get_settings_keyboard
from ..services.user_service import UserService

# Create router for text messages
text_router = Router()


@text_router.message(lambda m: m.text == "📊 Profile")
async def handle_profile_button(message: Message):
    """Handle profile button press"""
    try:
        log_user_action(message, "profile_button")

        user = await UserService.get_user_by_chat_id_async(message.from_user.id)

        if not user:
            await message.answer("❌ User profile not found. Please use /start first.")
            return

        created_at = user.created_at.strftime('%B %d, %Y') if user.created_at else 'Unknown'
        updated_at = user.updated_at.strftime('%B %d, %Y at %H:%M') if user.updated_at else 'Unknown'

        profile_text = (
            f"👤 <b>Your Profile</b>\n\n"
            f"<b>Name:</b> {user.first_name or 'Not set'}\n"
            f"<b>Username:</b> @{user.username or 'Not set'}\n"
            f"<b>Language:</b> {user.language_code or 'Not set'}\n"
            f"<b>Member since:</b> {created_at}\n"
            f"<b>Last updated:</b> {updated_at}\n\n"
            f"<b>Chat ID:</b> <code>{user.chat_id}</code>"
        )

        await message.answer(profile_text, parse_mode="HTML")

    except Exception as e:
        await send_error_message(message, e)


@text_router.message(lambda m: m.text == "⚙️ Settings")
async def handle_settings_button(message: Message):
    """Handle settings button press"""
    try:
        log_user_action(message, "settings_button")

        await message.answer(
            "⚙️ <b>Settings</b>\n\n"
            "Configure your bot preferences:",
            parse_mode="HTML",
            reply_markup=get_settings_keyboard()
        )

    except Exception as e:
        await send_error_message(message, e)


@text_router.message(lambda m: m.text == "❓ Help")
async def handle_help_button(message: Message):
    """Handle help button press"""
    try:
        log_user_action(message, "help_button")

        help_text = (
            "🤖 <b>Bot Commands</b>\n\n"
            "/start - Start the bot and get main menu\n"
            "/help - Show this help message\n"
            "/profile - View your profile information\n\n"
            "<b>Features:</b>\n"
            "• User management and tracking\n"
            "• Interactive keyboards\n"
            "• Error handling and logging\n"
            "• Django integration\n\n"
            "Need more help? Contact the bot administrator."
        )

        await message.answer(help_text, parse_mode="HTML")

    except Exception as e:
        await send_error_message(message, e)


@text_router.message(lambda m: m.text == "📞 Contact")
async def handle_contact_button(message: Message):
    """Handle contact button press"""
    try:
        log_user_action(message, "contact_button")

        contact_text = (
            "📞 <b>Contact Information</b>\n\n"
            "<b>Bot Administrator:</b> @admin_username\n"
            "<b>Email:</b> <EMAIL>\n"
            "<b>Support:</b> <EMAIL>\n\n"
            "Feel free to reach out if you need help or have suggestions!"
        )

        await message.answer(contact_text, parse_mode="HTML")

    except Exception as e:
        await send_error_message(message, e)


@text_router.message()
async def handle_unknown_text(message: Message):
    """Handle unknown text messages"""
    try:
        log_user_action(message, "unknown_text")

        await message.answer(
            "🤔 I didn't understand that message.\n\n"
            "Please use the buttons below or type /help for available commands:",
            reply_markup=get_main_keyboard()
        )

    except Exception as e:
        await send_error_message(message, e)