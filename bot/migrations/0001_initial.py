# Generated by Django 5.2.3 on 2025-06-30 02:28

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('chat_id', models.BigIntegerField(unique=True)),
                ('username', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('first_name', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ('last_name', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('language_code', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
