import asyncio
import logging
from django.core.management.base import <PERSON><PERSON>ommand
from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.enums import ParseMode
from aiogram.fsm.storage.memory import MemoryStorage
from aiogram.client.default import DefaultBotProperties
from environs import Env

from bot.handlers import start_router, help_router, profile_router, callback_router, text_router
from bot.middleware import DatabaseMiddleware, LoggingMiddleware

env = Env()
env.read_env()

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Run the Telegram bot'

    def handle(self, *args, **options):
        """
        Handle the command
        """
        token = env.str('TELEGRAM_BOT_TOKEN')
        asyncio.run(self.run_bot(token))

    async def run_bot(self, token: str):
        """
        Run the bot with async event loop
        """
        try:
            bot = Bot(
                token=token,
                default=DefaultBotProperties(parse_mode=ParseMode.MARKDOWN)
            )
            dp = self.get_dispatcher()
            
            self.stdout.write(
                self.style.SUCCESS('Starting bot polling...')
            )
            
            await dp.start_polling(bot)
            
        except Exception as e:
            self.stderr.write(
                self.style.ERROR(f'Error running bot: {e}')
            )
            raise

    def get_dispatcher(self) -> Dispatcher:
        """
        Get configured dispatcher
        """
        dp = Dispatcher(storage=MemoryStorage())

        # Register middlewares
        dp.message.middleware(DatabaseMiddleware())
        dp.message.middleware(LoggingMiddleware())
        dp.callback_query.middleware(DatabaseMiddleware())
        dp.callback_query.middleware(LoggingMiddleware())

        # Register routers
        dp.include_router(start_router)
        dp.include_router(help_router)
        dp.include_router(profile_router)
        dp.include_router(callback_router)
        dp.include_router(text_router)

        return dp
