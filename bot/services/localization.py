from typing import Dict, Any
from ..models import User


class LocalizationService:
    """Service for handling bot localization"""
    
    # Localization data
    TEXTS = {
        'en': {
            # Start messages
            'language_selection': "Please choose your language:",
            'welcome_message': "👋 Welcome, {name}!\n\nI'm your Telegram bot assistant. Here's what I can do:\n• Help you with various tasks\n• Provide information and support\n• And much more!\n\nUse the buttons below to get started:",
            'language_set': "🇬🇧 English selected!\n\nWelcome!",
            
            # Main menu buttons
            'btn_profile': "📊 Profile",
            'btn_settings': "⚙️ Settings", 
            'btn_help': "❓ Help",
            'btn_contact': "📞 Contact",
            
            # Settings
            'settings_title': "⚙️ <b>Settings</b>\n\nConfigure your bot preferences:",
            'btn_notifications': "🔔 Notifications",
            'btn_language': "🌍 Language",
            'btn_privacy': "🔒 Privacy",
            'btn_back_main': "⬅️ Back to Main",
            'btn_back_settings': "⬅️ Back to Settings",
            
            # Language selection
            'language_settings_title': "🌍 <b>Language Settings</b>\n\nSelect your preferred language:",
            'btn_english': "🇬🇧 English",
            'btn_russian': "🇷🇺 Русский", 
            'btn_uzbek': "🇺🇿 O'zbek",
            
            # Profile
            'profile_title': "👤 <b>Your Profile</b>",
            'profile_name': "<b>Name:</b> {name}",
            'profile_username': "<b>Username:</b> @{username}",
            'profile_language': "<b>Language:</b> {language}",
            'profile_member_since': "<b>Member since:</b> {date}",
            'profile_last_updated': "<b>Last updated:</b> {date}",
            'profile_chat_id': "<b>Chat ID:</b> <code>{chat_id}</code>",
            'profile_not_found': "❌ User profile not found. Please use /start first.",
            'not_set': "Not set",
            
            # Help
            'help_title': "🤖 <b>Bot Commands</b>",
            'help_commands': "/start - Start the bot and get main menu\n/help - Show this help message\n/profile - View your profile information",
            'help_features': "<b>Features:</b>\n• User management and tracking\n• Interactive keyboards\n• Error handling and logging\n• Django integration",
            'help_footer': "Need more help? Contact the bot administrator.",
            
            # Contact
            'contact_title': "📞 <b>Contact Information</b>",
            'contact_admin': "<b>Bot Administrator:</b> @admin_username",
            'contact_email': "<b>Email:</b> <EMAIL>",
            'contact_support': "<b>Support:</b> <EMAIL>",
            'contact_footer': "Feel free to reach out if you need help or have suggestions!",
            
            # Navigation
            'main_menu_title': "🏠 <b>Main Menu</b>\n\nWelcome back! What would you like to do?",
            
            # Notifications and Privacy (placeholders)
            'notifications_title': "🔔 <b>Notification Settings</b>\n\nConfigure your notification preferences here.\nThis feature is coming soon!",
            'privacy_title': "🔒 <b>Privacy Settings</b>\n\nManage your privacy preferences here.\nThis feature is coming soon!",
            
            # Confirmations
            'btn_yes': "✅ Yes",
            'btn_no': "❌ No",
            'confirmed': "✅ <b>Confirmed!</b>\n\nYour action has been completed successfully.",
            'cancelled': "❌ <b>Cancelled</b>\n\nAction has been cancelled.",
            
            # Unknown message
            'unknown_message': "🤔 I didn't understand that message.\n\nPlease use the buttons below or type /help for available commands:",
        },
        
        'ru': {
            # Start messages
            'language_selection': "Пожалуйста, выберите язык:",
            'welcome_message': "👋 Добро пожаловать, {name}!\n\nЯ ваш Telegram-бот помощник. Вот что я умею:\n• Помогу с разными задачами\n• Предоставлю информацию и поддержку\n• И многое другое!\n\nИспользуйте кнопки ниже, чтобы начать:",
            'language_set': "🇷🇺 Язык выбран!\n\nДобро пожаловать!",
            
            # Main menu buttons
            'btn_profile': "📊 Профиль",
            'btn_settings': "⚙️ Настройки",
            'btn_help': "❓ Помощь", 
            'btn_contact': "📞 Контакты",
            
            # Settings
            'settings_title': "⚙️ <b>Настройки</b>\n\nНастройте параметры бота:",
            'btn_notifications': "🔔 Уведомления",
            'btn_language': "🌍 Язык",
            'btn_privacy': "🔒 Приватность",
            'btn_back_main': "⬅️ В главное меню",
            'btn_back_settings': "⬅️ К настройкам",
            
            # Language selection
            'language_settings_title': "🌍 <b>Настройки языка</b>\n\nВыберите предпочитаемый язык:",
            'btn_english': "🇬🇧 English",
            'btn_russian': "🇷🇺 Русский",
            'btn_uzbek': "🇺🇿 O'zbek",
            
            # Profile
            'profile_title': "👤 <b>Ваш профиль</b>",
            'profile_name': "<b>Имя:</b> {name}",
            'profile_username': "<b>Имя пользователя:</b> @{username}",
            'profile_language': "<b>Язык:</b> {language}",
            'profile_member_since': "<b>Участник с:</b> {date}",
            'profile_last_updated': "<b>Последнее обновление:</b> {date}",
            'profile_chat_id': "<b>ID чата:</b> <code>{chat_id}</code>",
            'profile_not_found': "❌ Профиль пользователя не найден. Пожалуйста, используйте /start сначала.",
            'not_set': "Не установлено",
            
            # Help
            'help_title': "🤖 <b>Команды бота</b>",
            'help_commands': "/start - Запустить бота и получить главное меню\n/help - Показать это сообщение помощи\n/profile - Посмотреть информацию профиля",
            'help_features': "<b>Возможности:</b>\n• Управление пользователями и отслеживание\n• Интерактивные клавиатуры\n• Обработка ошибок и логирование\n• Интеграция с Django",
            'help_footer': "Нужна дополнительная помощь? Свяжитесь с администратором бота.",
            
            # Contact
            'contact_title': "📞 <b>Контактная информация</b>",
            'contact_admin': "<b>Администратор бота:</b> @admin_username",
            'contact_email': "<b>Email:</b> <EMAIL>",
            'contact_support': "<b>Поддержка:</b> <EMAIL>",
            'contact_footer': "Не стесняйтесь обращаться, если вам нужна помощь или у вас есть предложения!",
            
            # Navigation
            'main_menu_title': "🏠 <b>Главное меню</b>\n\nДобро пожаловать обратно! Что вы хотите сделать?",
            
            # Notifications and Privacy (placeholders)
            'notifications_title': "🔔 <b>Настройки уведомлений</b>\n\nНастройте параметры уведомлений здесь.\nЭта функция скоро появится!",
            'privacy_title': "🔒 <b>Настройки приватности</b>\n\nУправляйте настройками приватности здесь.\nЭта функция скоро появится!",
            
            # Confirmations
            'btn_yes': "✅ Да",
            'btn_no': "❌ Нет",
            'confirmed': "✅ <b>Подтверждено!</b>\n\nВаше действие было успешно выполнено.",
            'cancelled': "❌ <b>Отменено</b>\n\nДействие было отменено.",
            
            # Unknown message
            'unknown_message': "🤔 Я не понял это сообщение.\n\nПожалуйста, используйте кнопки ниже или введите /help для доступных команд:",
        },
        
        'uz': {
            # Start messages
            'language_selection': "Iltimos, tilni tanlang:",
            'welcome_message': "👋 Xush kelibsiz, {name}!\n\nMen sizning Telegram bot yordamchingizman. Mana nimalar qila olaman:\n• Turli vazifalarda yordam beraman\n• Ma'lumot va yordam taqdim etaman\n• Yana ko'p narsalar!\n\nBoshlash uchun quyidagi tugmalardan foydalaning:",
            'language_set': "🇺🇿 Til tanlandi!\n\nXush kelibsiz!",
            
            # Main menu buttons
            'btn_profile': "📊 Profil",
            'btn_settings': "⚙️ Sozlamalar",
            'btn_help': "❓ Yordam",
            'btn_contact': "📞 Aloqa",
            
            # Settings
            'settings_title': "⚙️ <b>Sozlamalar</b>\n\nBot sozlamalarini o'zgartiring:",
            'btn_notifications': "🔔 Bildirishnomalar",
            'btn_language': "🌍 Til",
            'btn_privacy': "🔒 Maxfiylik",
            'btn_back_main': "⬅️ Asosiy menyuga",
            'btn_back_settings': "⬅️ Sozlamalarga",
            
            # Language selection
            'language_settings_title': "🌍 <b>Til sozlamalari</b>\n\nKerakli tilni tanlang:",
            'btn_english': "🇬🇧 English",
            'btn_russian': "🇷🇺 Русский",
            'btn_uzbek': "🇺🇿 O'zbek",
            
            # Profile
            'profile_title': "👤 <b>Sizning profilingiz</b>",
            'profile_name': "<b>Ism:</b> {name}",
            'profile_username': "<b>Foydalanuvchi nomi:</b> @{username}",
            'profile_language': "<b>Til:</b> {language}",
            'profile_member_since': "<b>A'zo bo'lgan sana:</b> {date}",
            'profile_last_updated': "<b>Oxirgi yangilanish:</b> {date}",
            'profile_chat_id': "<b>Chat ID:</b> <code>{chat_id}</code>",
            'profile_not_found': "❌ Foydalanuvchi profili topilmadi. Iltimos, avval /start buyrug'ini ishlating.",
            'not_set': "O'rnatilmagan",
            
            # Help
            'help_title': "🤖 <b>Bot buyruqlari</b>",
            'help_commands': "/start - Botni ishga tushirish va asosiy menyuni olish\n/help - Ushbu yordam xabarini ko'rsatish\n/profile - Profil ma'lumotlarini ko'rish",
            'help_features': "<b>Imkoniyatlar:</b>\n• Foydalanuvchilarni boshqarish va kuzatish\n• Interaktiv klaviaturalar\n• Xatolarni qayta ishlash va jurnallash\n• Django bilan integratsiya",
            'help_footer': "Qo'shimcha yordam kerakmi? Bot administratori bilan bog'laning.",
            
            # Contact
            'contact_title': "📞 <b>Aloqa ma'lumotlari</b>",
            'contact_admin': "<b>Bot administratori:</b> @admin_username",
            'contact_email': "<b>Email:</b> <EMAIL>",
            'contact_support': "<b>Qo'llab-quvvatlash:</b> <EMAIL>",
            'contact_footer': "Yordam kerak bo'lsa yoki takliflaringiz bo'lsa, bemalol murojaat qiling!",
            
            # Navigation
            'main_menu_title': "🏠 <b>Asosiy menyu</b>\n\nXush kelibsiz! Nima qilmoqchisiz?",
            
            # Notifications and Privacy (placeholders)
            'notifications_title': "🔔 <b>Bildirishnoma sozlamalari</b>\n\nBildirishnoma sozlamalarini bu yerda o'zgartiring.\nBu funksiya tez orada paydo bo'ladi!",
            'privacy_title': "🔒 <b>Maxfiylik sozlamalari</b>\n\nMaxfiylik sozlamalarini bu yerda boshqaring.\nBu funksiya tez orada paydo bo'ladi!",
            
            # Confirmations
            'btn_yes': "✅ Ha",
            'btn_no': "❌ Yo'q",
            'confirmed': "✅ <b>Tasdiqlandi!</b>\n\nSizning harakatingiz muvaffaqiyatli bajarildi.",
            'cancelled': "❌ <b>Bekor qilindi</b>\n\nHarakat bekor qilindi.",
            
            # Unknown message
            'unknown_message': "🤔 Men bu xabarni tushunmadim.\n\nIltimos, quyidagi tugmalardan foydalaning yoki mavjud buyruqlar uchun /help yozing:",
        }
    }
    
    @staticmethod
    def get_text(key: str, lang: str = 'en', **kwargs) -> str:
        """Get localized text by key and language"""
        # Default to English if language not found
        if lang not in LocalizationService.TEXTS:
            lang = 'en'
            
        # Get text from localization data
        text = LocalizationService.TEXTS.get(lang, {}).get(key, 
               LocalizationService.TEXTS['en'].get(key, f"Missing text: {key}"))
        
        # Format with provided kwargs
        try:
            return text.format(**kwargs)
        except (KeyError, ValueError):
            return text
    
    @staticmethod
    async def get_user_language(chat_id: int) -> str:
        """Get user's language from database"""
        from .user_service import UserService
        user = await UserService.get_user_by_chat_id_async(chat_id)
        return user.language_code if user and user.language_code else 'en'
    
    @staticmethod
    def get_language_name(lang_code: str) -> str:
        """Get language display name"""
        names = {
            'en': 'English',
            'ru': 'Русский', 
            'uz': "O'zbek"
        }
        return names.get(lang_code, lang_code)
