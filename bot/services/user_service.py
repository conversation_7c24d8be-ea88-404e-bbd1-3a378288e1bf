from typing import Optional, List
from ..models import User
import logging
from asgiref.sync import sync_to_async

logger = logging.getLogger(__name__)


class UserService:
    """Simple service for user operations using Django ORM"""

    @staticmethod
    def create_or_get_user(chat_id: int, username: Optional[str] = None,
                          first_name: Optional[str] = None, last_name: Optional[str] = None,
                          language_code: Optional[str] = None) -> User:
        """Create or get user using Django ORM"""
        try:
            user, created = User.objects.get_or_create(
                chat_id=chat_id,
                defaults={
                    'username': username,
                    'first_name': first_name,
                    'last_name': last_name,
                    'language_code': language_code,
                }
            )

            # Update user info if not created (existing user)
            if not created:
                user.username = username
                user.first_name = first_name
                user.last_name = last_name
                # Only update language_code if it's not already set
                if not user.language_code and language_code:
                    user.language_code = language_code
                user.save()

            return user

        except Exception as e:
            logger.error(f"Error in create_or_get_user: {e}")
            raise

    @staticmethod
    async def create_or_get_user_async(chat_id: int, username: Optional[str] = None,
                                      first_name: Optional[str] = None, last_name: Optional[str] = None,
                                      language_code: Optional[str] = None) -> User:
        """Async version of create_or_get_user"""
        return await sync_to_async(UserService.create_or_get_user)(
            chat_id, username, first_name, last_name, language_code
        )

    @staticmethod
    def get_user_by_chat_id(chat_id: int) -> Optional[User]:
        """Get user by chat_id"""
        try:
            return User.objects.filter(chat_id=chat_id).first()
        except Exception as e:
            logger.error(f"Error in get_user_by_chat_id: {e}")
            raise

    @staticmethod
    async def get_user_by_chat_id_async(chat_id: int) -> Optional[User]:
        """Async version of get_user_by_chat_id"""
        return await sync_to_async(UserService.get_user_by_chat_id)(chat_id)

    @staticmethod
    def update_user(chat_id: int, **kwargs) -> bool:
        """Update user fields"""
        try:
            user = User.objects.filter(chat_id=chat_id).first()
            if not user:
                return False

            for field, value in kwargs.items():
                if hasattr(user, field):
                    setattr(user, field, value)

            user.save()
            return True

        except Exception as e:
            logger.error(f"Error in update_user: {e}")
            raise

    @staticmethod
    async def update_user_async(chat_id: int, **kwargs) -> bool:
        """Async version of update_user"""
        return await sync_to_async(UserService.update_user)(chat_id, **kwargs)

    @staticmethod
    def get_all_users() -> List[User]:
        """Get all users"""
        try:
            return list(User.objects.all().order_by('-created_at'))
        except Exception as e:
            logger.error(f"Error getting all users: {e}")
            raise

    @staticmethod
    async def get_all_users_async() -> List[User]:
        """Async version of get_all_users"""
        return await sync_to_async(UserService.get_all_users)()

    @staticmethod
    def get_active_users(days: int = 30) -> List[User]:
        """Get users active in the last N days"""
        try:
            from django.utils import timezone
            from datetime import timedelta

            cutoff_date = timezone.now() - timedelta(days=days)
            return list(User.objects.filter(updated_at__gte=cutoff_date).order_by('-updated_at'))
        except Exception as e:
            logger.error(f"Error getting active users: {e}")
            raise

    @staticmethod
    async def get_active_users_async(days: int = 30) -> List[User]:
        """Async version of get_active_users"""
        return await sync_to_async(UserService.get_active_users)(days)

    @staticmethod
    def delete_user(chat_id: int) -> bool:
        """Delete user by chat ID"""
        try:
            deleted, _ = User.objects.filter(chat_id=chat_id).delete()
            if deleted:
                logger.info(f"Deleted user: {chat_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting user {chat_id}: {e}")
            raise

    @staticmethod
    async def delete_user_async(chat_id: int) -> bool:
        """Async version of delete_user"""
        return await sync_to_async(UserService.delete_user)(chat_id)

    @staticmethod
    def get_user_stats() -> dict:
        """Get user statistics"""
        try:
            from django.utils import timezone
            from datetime import timedelta

            total_users = User.objects.count()
            today = timezone.now().date()
            week_ago = today - timedelta(days=7)
            month_ago = today - timedelta(days=30)

            new_users_week = User.objects.filter(created_at__date__gte=week_ago).count()
            new_users_month = User.objects.filter(created_at__date__gte=month_ago).count()

            return {
                'total_users': total_users,
                'new_users_week': new_users_week,
                'new_users_month': new_users_month,
            }
        except Exception as e:
            logger.error(f"Error getting user stats: {e}")
            raise

    @staticmethod
    async def get_user_stats_async() -> dict:
        """Async version of get_user_stats"""
        return await sync_to_async(UserService.get_user_stats)()
